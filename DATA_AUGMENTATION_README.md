# 专利级数据增强系统

基于发明专利《一种基于大语言模型特征增强的多模态网络谣言检测方法》实现的数据增强模块。

## 🎯 核心功能

### 三类专利级提示词增强

1. **Waug1 - 规则化文本操作**
   - 对原始文本进行规范化处理
   - 优化语法和表达方式
   - 保持原意的同时提升文本质量

2. **Waug2 - 多样化重描述**
   - 用不同表达方式重新描述内容
   - 同义词替换和句式变换
   - 增加文本的多样性

3. **Waug3 - 图像详细描述**
   - 基于文本内容生成可能配图的详细描述
   - 补充视觉信息维度
   - 增强多模态特征

### 支持的API

- **DeepSeek API** (默认推荐)
- **OpenAI GPT-4 API**
- **通义千问 API**

## 🚀 快速开始

### 1. 配置API密钥

编辑 `src/config.py` 文件，填入您的API密钥：

```python
# DeepSeek API 配置 (推荐)
self.deepseek_api_key = "your_deepseek_api_key"

# OpenAI API 配置 (可选)
self.openai_api_key = "your_openai_api_key"

# 通义千问 API 配置 (可选)
self.qwen_api_key = "your_qwen_api_key"
```

### 2. 测试API连接

```bash
python scripts/test_augmentation.py
```

### 3. 增强数据集

```bash
# 基本用法
python scripts/augment_data.py -i data/train.csv -o data/train_augmented.csv

# 指定参数
python scripts/augment_data.py \
  -i data/train.csv \
  -o data/train_augmented.csv \
  --text-column text \
  --workers 4 \
  --api-type deepseek
```

## 📊 使用示例

### 命令行增强

```bash
# 1. 增强训练数据
python scripts/augment_data.py -i data/train.csv -o data/train_augmented.csv

# 2. 增强验证数据
python scripts/augment_data.py -i data/val.csv -o data/val_augmented.csv

# 3. 使用多线程加速
python scripts/augment_data.py -i data/train.csv -o data/train_augmented.csv --workers 8

# 4. 测试API连接
python scripts/augment_data.py --test-api --api-type deepseek
```

### 编程接口

```python
from src.config import Config
from src.data.augmentation import PatentLLMDataAugmentation

# 初始化
config = Config()
augmenter = PatentLLMDataAugmentation(config)

# 单条文本增强
text = "某地发生重大事故，造成多人伤亡"
result = augmenter.generate_patent_augmentation(text)

print("原始文本:", result['original'])
print("规则化增强:", result['waug1'])
print("多样化重描述:", result['waug2'])
print("图像描述:", result['waug3'])
print("融合增强:", result['waug_fused'])

# 批量增强
texts = ["文本1", "文本2", "文本3"]
results = augmenter.batch_augment_dataset(texts)
```

## 💾 缓存机制

### 自动缓存
- 所有API调用结果自动缓存到 `cache/patent_augmentation/`
- 相同文本的重复增强直接使用缓存，避免重复API调用
- 大幅节省API费用和处理时间

### 缓存管理
```bash
# 查看缓存统计
python scripts/augment_data.py --cache-stats

# 清理所有缓存
python scripts/augment_data.py --clear-cache

# 不使用缓存（强制重新生成）
python scripts/augment_data.py -i input.csv -o output.csv --no-cache
```

## 📈 数据增强效果

### 输入数据格式
```csv
text,label,image
"某地发生重大事故，造成多人伤亡",1,""
"专家表示新技术将改变行业",0,""
```

### 输出数据格式
```csv
text,label,image,augmentation_type,waug3_description
"某地发生重大事故，造成多人伤亡",1,"","original",""
"某地区出现严重交通意外，导致多人死伤",1,"","waug1",""
"当地发生重大交通事故，引起大量人员伤亡",1,"","waug2",""
"某地发生重大事故，造成多人伤亡 [配图描述: 事故现场...]",1,"","waug_fused","事故现场显示..."
```

### 数据量增长
- 原始数据：N条
- 增强后数据：4N条 (原始 + Waug1 + Waug2 + 融合)
- 增长倍数：4x

## ⚙️ 高级配置

### API参数调整
在 `src/config.py` 中可以调整：
- `max_tokens`: 生成文本的最大长度
- `temperature`: 生成文本的随机性 (0-1)
- `model`: 使用的具体模型版本

### 并发控制
- `--workers`: 并行线程数，建议根据API限制调整
- 默认4线程，可根据需要增加到8-16

### 错误处理
- API调用失败时自动重试
- 超时保护 (60秒)
- 失败时返回原文本，确保数据完整性

## 🔧 故障排除

### 常见问题

1. **API连接失败**
   ```bash
   # 测试连接
   python scripts/augment_data.py --test-api
   ```

2. **缓存问题**
   ```bash
   # 清理缓存重试
   python scripts/augment_data.py --clear-cache
   ```

3. **内存不足**
   ```bash
   # 减少并发线程
   python scripts/augment_data.py -i input.csv -o output.csv --workers 2
   ```

### 性能优化

- 首次运行较慢（需要API调用）
- 后续运行快速（使用缓存）
- 建议先用小数据集测试
- 大数据集可分批处理

## 📝 注意事项

1. **API费用**: 大语言模型API调用可能产生费用，请注意控制用量
2. **网络要求**: 需要稳定的网络连接访问API服务
3. **数据安全**: 文本内容会发送到第三方API，请注意数据隐私
4. **缓存管理**: 定期清理缓存以释放磁盘空间

## 🎯 最佳实践

1. **先测试后批量**: 使用小数据集测试效果后再处理大数据集
2. **合理使用缓存**: 开发阶段使用缓存，生产环境可考虑清理
3. **API选择**: DeepSeek性价比高，GPT-4效果更好，根据需求选择
4. **分批处理**: 大数据集建议分批处理，避免长时间运行风险

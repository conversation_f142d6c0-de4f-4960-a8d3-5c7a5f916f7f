# 使用说明

## 🚀 快速开始

### 1. 训练模型
```bash
python scripts/train.py
```

### 2. 运行推理
```bash
python scripts/inference.py
```

### 3. 测试模型
```bash
python scripts/test_model.py
```

### 4. 数据处理
```bash
python scripts/process_data.py
```

## 📁 项目结构

```
pfncm/
├── src/                        # 源代码
│   ├── config.py               # 配置文件
│   ├── models/                 # 模型定义
│   ├── data/                   # 数据处理
│   ├── training/               # 训练模块
│   └── utils/                  # 工具函数
├── scripts/                    # 可执行脚本
│   ├── train.py                # 训练脚本
│   ├── inference.py            # 推理脚本
│   ├── test_model.py           # 模型测试
│   └── process_data.py         # 数据处理
├── data/                       # 数据文件
│   ├── train.csv               # 训练数据
│   ├── val.csv                 # 验证数据
│   ├── test.csv                # 测试数据
│   └── images/                 # 图像文件
├── outputs/                    # 输出文件
│   ├── checkpoints/            # 模型检查点
│   ├── logs/                   # 训练日志
│   └── results/                # 结果文件
└── cache/                      # 缓存文件
    └── models_cache/           # 预训练模型缓存
```

## ⚙️ 配置说明

主要配置在 `src/config.py` 中：

- **模型配置**: CLIP和RoBERTa模型参数
- **训练配置**: 学习率、批次大小、训练轮数等
- **路径配置**: 数据路径、模型保存路径等
- **设备配置**: 自动检测GPU/CPU

## 📊 数据格式

CSV文件格式：
- `text`: 新闻文本内容
- `label`: 标签（0=真实，1=虚假）
- `image`: 图像路径（可选）

## 🔧 常见问题

### 1. 模型加载兼容性问题
如果遇到模型加载错误，脚本会自动尝试兼容性加载，跳过不匹配的权重。

### 2. GPU内存不足
可以在 `src/config.py` 中调整 `batch_size` 参数。

### 3. 依赖安装
```bash
pip install -r requirements.txt
```

## 📈 模型性能

训练完成后可以查看：
- 训练日志: `outputs/logs/`
- 模型检查点: `outputs/checkpoints/`
- 测试结果: 运行 `python scripts/test_model.py`

## 🎯 自定义使用

### 修改配置
编辑 `src/config.py` 中的参数

### 添加新数据
将数据放入 `data/` 目录，格式参考现有CSV文件

### 自定义推理
修改 `scripts/inference.py` 中的示例文本和图像路径

import time
from datetime import timed<PERSON><PERSON>

def get_time_dif(start_time):
    """获取已使用时间"""
    end_time = time.time()
    time_dif = end_time - start_time
    return timedelta(seconds=int(round(time_dif)))

def format_time(seconds):
    """格式化时间显示"""
    return str(timedelta(seconds=int(seconds)))

def print_progress(current, total, prefix='Progress', suffix='Complete', length=50):
    """打印进度条"""
    percent = ("{0:.1f}").format(100 * (current / float(total)))
    filled_length = int(length * current // total)
    bar = '█' * filled_length + '-' * (length - filled_length)
    print(f'\r{prefix} |{bar}| {percent}% {suffix}', end='')
    if current == total:
        print()
import torch
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR
import numpy as np
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, classification_report
import time
import os
from ..utils.common import get_time_dif

class Trainer:
    """训练器"""
    
    def __init__(self, config, model, train_loader, val_loader, test_loader):
        self.config = config
        self.model = model
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.test_loader = test_loader
        
        # 损失函数
        self.criterion = nn.CrossEntropyLoss()
        
        # 优化器
        self.optimizer = self._setup_optimizer()
        
        # 学习率调度器
        self.scheduler = CosineAnnealingLR(
            self.optimizer, 
            T_max=config.num_epochs,
            eta_min=1e-6
        )
        
        # 训练状态
        self.best_acc = 0.0
        self.best_f1 = 0.0
        self.patience_counter = 0
        
    def _setup_optimizer(self):
        """设置优化器"""
        # 分组参数，不同组使用不同学习率
        clip_params = []
        roberta_params = []
        attention_params = []
        other_params = []
        
        for name, param in self.model.named_parameters():
            if 'clip_model' in name:
                clip_params.append(param)
            elif 'roberta_model' in name:
                roberta_params.append(param)
            elif 'cross_attention' in name:
                attention_params.append(param)
            else:
                other_params.append(param)
        
        optimizer = optim.AdamW([
            {'params': clip_params, 'lr': self.config.clip_learning_rate},
            {'params': roberta_params, 'lr': self.config.roberta_learning_rate},
            {'params': attention_params, 'lr': self.config.attention_learning_rate},
            {'params': other_params, 'lr': self.config.other_learning_rate}
        ], weight_decay=self.config.weight_decay)
        
        return optimizer
    
    def train_epoch(self):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        all_preds = []
        all_labels = []
        
        for batch_idx, batch in enumerate(self.train_loader):
            # 准备数据
            inputs = {
                'images': batch['images'].to(self.config.device),
                'texts': batch['texts'],
                'enhanced_texts': batch['enhanced_texts'],
                'image_descriptions': batch['image_descriptions'],
                'emotion_factors': batch['emotion_factors']
            }
            labels = batch['labels'].to(self.config.device)
            
            # 前向传播
            self.optimizer.zero_grad()
            outputs = self.model(inputs)
            loss = self.criterion(outputs, labels)
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
            self.optimizer.step()
            
            # 统计
            total_loss += loss.item()
            preds = torch.argmax(outputs, dim=1)
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            
            if batch_idx % 10 == 0:
                print(f'Batch {batch_idx}/{len(self.train_loader)}, Loss: {loss.item():.4f}')
        
        # 计算指标
        avg_loss = total_loss / len(self.train_loader)
        accuracy = accuracy_score(all_labels, all_preds)
        precision, recall, f1, _ = precision_recall_fscore_support(
            all_labels, all_preds, average='weighted'
        )
        
        return {
            'loss': avg_loss,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1
        }
    
    def evaluate(self, data_loader):
        """评估模型"""
        self.model.eval()
        total_loss = 0
        all_preds = []
        all_labels = []
        
        with torch.no_grad():
            for batch in data_loader:
                inputs = {
                    'images': batch['images'].to(self.config.device),
                    'texts': batch['texts'],
                    'enhanced_texts': batch['enhanced_texts'],
                    'image_descriptions': batch['image_descriptions'],
                    'emotion_factors': batch['emotion_factors']
                }
                labels = batch['labels'].to(self.config.device)
                
                outputs = self.model(inputs)
                loss = self.criterion(outputs, labels)
                
                total_loss += loss.item()
                preds = torch.argmax(outputs, dim=1)
                all_preds.extend(preds.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
        
        avg_loss = total_loss / len(data_loader)
        accuracy = accuracy_score(all_labels, all_preds)
        precision, recall, f1, _ = precision_recall_fscore_support(
            all_labels, all_preds, average='weighted'
        )
        
        return {
            'loss': avg_loss,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1
        }
    
    def save_model(self, path, is_best=False):
        """保存模型"""
        torch.save(self.model.state_dict(), path)
        if is_best:
            print(f"✅ 最佳模型已保存: {path}")
    
    def train(self):
        """完整训练流程"""
        print("🚀 开始训练...")
        start_time = time.time()
        
        for epoch in range(self.config.num_epochs):
            print(f"\n📅 Epoch {epoch+1}/{self.config.num_epochs}")
            print("-" * 50)
            
            # 训练
            train_metrics = self.train_epoch()
            print(f"训练 - Loss: {train_metrics['loss']:.4f}, "
                  f"Acc: {train_metrics['accuracy']:.4f}, "
                  f"F1: {train_metrics['f1']:.4f}")
            
            # 验证
            val_metrics = self.evaluate(self.val_loader)
            print(f"验证 - Loss: {val_metrics['loss']:.4f}, "
                  f"Acc: {val_metrics['accuracy']:.4f}, "
                  f"F1: {val_metrics['f1']:.4f}")
            
            # 更新学习率
            self.scheduler.step()
            
            # 保存最佳模型
            if val_metrics['accuracy'] > self.best_acc:
                self.best_acc = val_metrics['accuracy']
                self.save_model(
                    self.config.save_path.replace('.pth', '_best_acc.pth'),
                    is_best=True
                )
                self.patience_counter = 0
            
            if val_metrics['f1'] > self.best_f1:
                self.best_f1 = val_metrics['f1']
                self.save_model(
                    self.config.save_path.replace('.pth', '_best_f1.pth'),
                    is_best=True
                )
            
            # 早停检查
            self.patience_counter += 1
            if self.patience_counter >= self.config.early_stopping_patience:
                print(f"⏰ 早停触发，训练结束")
                break
        
        # 测试
        print("\n🧪 开始测试...")
        test_metrics = self.evaluate(self.test_loader)
        print(f"测试结果 - Acc: {test_metrics['accuracy']:.4f}, "
              f"F1: {test_metrics['f1']:.4f}")
        
        total_time = get_time_dif(start_time)
        print(f"\n⏱️ 训练总时间: {total_time}")
        print(f"🏆 最佳验证准确率: {self.best_acc:.4f}")
        print(f"🏆 最佳验证F1: {self.best_f1:.4f}")

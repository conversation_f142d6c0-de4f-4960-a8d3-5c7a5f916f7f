import os
import torch

class Config:
    """统一配置管理"""
    def __init__(self):
        # 设备配置
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 基础训练参数
        self.num_classes = 2
        self.num_epochs = 20
        self.batch_size = 32
        self.dropout = 0.3
        
        # 智能训练配置
        self.require_improvement = 1000
        self.save_best_models = True
        self.monitor_metric = 'f1_score'

        # 学习率配置
        self.clip_learning_rate = 1e-5
        self.roberta_learning_rate = 2e-5
        self.attention_learning_rate = 1e-4
        self.other_learning_rate = 2e-5
        
        # 模型配置
        self.clip_model_name = 'ViT-B/32'
        self.roberta_model_name = 'iic/nlp_roberta_backbone_large_std'
        self.clip_feature_dim = 512
        self.roberta_feature_dim = 1024

        # ModelScope配置
        self.use_modelscope = True
        self.modelscope_cache_dir = './cache/models_cache/'  # 更新为正确路径
        
        # 融合机制参数
        self.num_attention_heads = 8
        self.fusion_hidden_dim = 512
        self.lambda_init = 0.5
        
        # 数据增强配置
        self.enable_llm_augmentation = False
        self.enable_emotion_enhancement = False
        self.augmentation_ratio = 0.0
        self.image_enhancement_ratio = 0.0
        self.max_api_calls_per_epoch = 500
        self.emotion_weight_min = 0.1
        
        # 数据配置
        self.frac = 1.0
        self.pad_size = 128
        
        # 路径配置
        self._setup_paths()
        
        # 提示学习模板
        self.prompt_template = "这是一则新闻：{text}。配图显示：{image_desc}。请分析此新闻的真实性。此报道[MASK]。"
        
        # 专利级数据增强API配置
        # DeepSeek API 配置
        self.deepseek_api_key = "***********************************"
        self.deepseek_base_url = "https://api.deepseek.com"
        self.deepseek_model = "deepseek-chat"
        self.deepseek_max_tokens = 150
        self.deepseek_temperature = 0.7

        # OpenAI GPT-4 API 配置
        self.openai_api_key = ""  # 请填入您的OpenAI API密钥
        self.openai_base_url = "https://api.openai.com/v1"
        self.openai_model = "gpt-4"
        self.openai_max_tokens = 150
        self.openai_temperature = 0.7

        # 通义千问 API 配置
        self.qwen_api_key = ""  # 请填入您的通义千问API密钥
        self.qwen_base_url = "https://dashscope.aliyuncs.com/api/v1"
        self.qwen_model = "qwen-turbo"
        self.qwen_max_tokens = 150
        self.qwen_temperature = 0.7
        
        # 正则化配置
        self.dropout_rate = 0.3
        self.weight_decay = 1e-4
        self.early_stopping_patience = 5
        self.augmentation_strength = 0.8
        self.learning_rate = 1e-5
        self.warmup_steps = 1000
    
    def _setup_paths(self):
        """设置路径"""
        # 创建必要目录
        os.makedirs('outputs/checkpoints', exist_ok=True)
        os.makedirs('outputs/logs', exist_ok=True)
        os.makedirs('data', exist_ok=True)
        os.makedirs('cache', exist_ok=True)

        # 数据路径
        self.data_dir = './data'
        self.train_path = os.path.join(self.data_dir, 'train.csv')
        self.val_path = os.path.join(self.data_dir, 'val.csv')
        self.test_path = os.path.join(self.data_dir, 'test.csv')

        # 模型保存路径
        self.save_path = 'outputs/checkpoints/best_model.pth'
        self.save_best_acc = True
        self.save_best_f1 = True
        self.log_dir = './outputs/logs/enhanced_multimodal'

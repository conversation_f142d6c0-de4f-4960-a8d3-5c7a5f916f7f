import torch
import torch.nn as nn
import torch.nn.functional as F

class CrossModalAttention(nn.Module):
    """跨模态注意力机制"""
    
    def __init__(self, clip_dim, roberta_dim, num_heads=8):
        super(CrossModalAttention, self).__init__()
        self.num_heads = num_heads
        self.clip_dim = clip_dim
        self.roberta_dim = roberta_dim
        
        # 确保维度可以被头数整除
        self.head_dim = max(clip_dim, roberta_dim) // num_heads
        self.hidden_dim = self.head_dim * num_heads
        
        # 投影层
        self.clip_to_hidden = nn.Linear(clip_dim, self.hidden_dim)
        self.roberta_to_hidden = nn.Linear(roberta_dim, self.hidden_dim)
        
        # 注意力层
        self.query_clip = nn.Linear(self.hidden_dim, self.hidden_dim)
        self.key_roberta = nn.Linear(self.hidden_dim, self.hidden_dim)
        self.value_roberta = nn.Linear(self.hidden_dim, self.hidden_dim)
        
        self.query_roberta = nn.Linear(self.hidden_dim, self.hidden_dim)
        self.key_clip = nn.Linear(self.hidden_dim, self.hidden_dim)
        self.value_clip = nn.Linear(self.hidden_dim, self.hidden_dim)
        
        # 输出投影
        self.output_clip = nn.Linear(self.hidden_dim, clip_dim)
        self.output_roberta = nn.Linear(self.hidden_dim, roberta_dim)
        
        # Layer Norm
        self.norm_clip = nn.LayerNorm(clip_dim)
        self.norm_roberta = nn.LayerNorm(roberta_dim)
        
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, clip_features, roberta_features):
        """
        Args:
            clip_features: [batch_size, clip_dim]
            roberta_features: [batch_size, roberta_dim]
        """
        batch_size = clip_features.size(0)
        
        # 投影到统一维度
        clip_hidden = self.clip_to_hidden(clip_features)  # [batch_size, hidden_dim]
        roberta_hidden = self.roberta_to_hidden(roberta_features)  # [batch_size, hidden_dim]
        
        # 添加序列维度用于注意力计算
        clip_hidden = clip_hidden.unsqueeze(1)  # [batch_size, 1, hidden_dim]
        roberta_hidden = roberta_hidden.unsqueeze(1)  # [batch_size, 1, hidden_dim]
        
        # CLIP -> RoBERTa 注意力
        q_clip = self.query_clip(clip_hidden)  # [batch_size, 1, hidden_dim]
        k_roberta = self.key_roberta(roberta_hidden)  # [batch_size, 1, hidden_dim]
        v_roberta = self.value_roberta(roberta_hidden)  # [batch_size, 1, hidden_dim]
        
        # 重塑为多头
        q_clip = q_clip.view(batch_size, 1, self.num_heads, self.head_dim).transpose(1, 2)
        k_roberta = k_roberta.view(batch_size, 1, self.num_heads, self.head_dim).transpose(1, 2)
        v_roberta = v_roberta.view(batch_size, 1, self.num_heads, self.head_dim).transpose(1, 2)
        
        # 计算注意力
        attn_scores = torch.matmul(q_clip, k_roberta.transpose(-2, -1)) / (self.head_dim ** 0.5)
        attn_weights = F.softmax(attn_scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        attended_clip = torch.matmul(attn_weights, v_roberta)
        attended_clip = attended_clip.transpose(1, 2).contiguous().view(batch_size, 1, self.hidden_dim)
        attended_clip = attended_clip.squeeze(1)  # [batch_size, hidden_dim]
        
        # RoBERTa -> CLIP 注意力
        q_roberta = self.query_roberta(roberta_hidden)
        k_clip = self.key_clip(clip_hidden)
        v_clip = self.value_clip(clip_hidden)
        
        q_roberta = q_roberta.view(batch_size, 1, self.num_heads, self.head_dim).transpose(1, 2)
        k_clip = k_clip.view(batch_size, 1, self.num_heads, self.head_dim).transpose(1, 2)
        v_clip = v_clip.view(batch_size, 1, self.num_heads, self.head_dim).transpose(1, 2)
        
        attn_scores = torch.matmul(q_roberta, k_clip.transpose(-2, -1)) / (self.head_dim ** 0.5)
        attn_weights = F.softmax(attn_scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        attended_roberta = torch.matmul(attn_weights, v_clip)
        attended_roberta = attended_roberta.transpose(1, 2).contiguous().view(batch_size, 1, self.hidden_dim)
        attended_roberta = attended_roberta.squeeze(1)
        
        # 输出投影
        output_clip = self.output_clip(attended_clip)
        output_roberta = self.output_roberta(attended_roberta)
        
        # 残差连接和层归一化
        output_clip = self.norm_clip(clip_features + output_clip)
        output_roberta = self.norm_roberta(roberta_features + output_roberta)
        
        return output_clip, output_roberta
import torch
import torch.nn as nn
import clip
from transformers import AutoModel, AutoTokenizer
from .attention import CrossModalAttention
from .prompt import PromptLearningLayer
import os

class EnhancedMultimodalNet(nn.Module):
    """增强多模态网络"""
    
    def __init__(self, config):
        super(EnhancedMultimodalNet, self).__init__()
        self.config = config
        
        # CLIP模型
        self.clip_model, self.clip_preprocess = clip.load(
            config.clip_model_name, 
            device=config.device
        )
        
        # RoBERTa模型
        print(f"🔍 当前工作目录: {os.getcwd()}")
        print(f"🔍 ModelScope缓存目录: {config.modelscope_cache_dir}")
        print(f"🔍 缓存目录是否存在: {os.path.exists(config.modelscope_cache_dir)}")
        
        # 直接使用本地模型路径
        local_model_path = os.path.join(config.modelscope_cache_dir, 'iic', 'nlp_roberta_backbone_large_std')
        print(f"🔍 本地模型路径: {local_model_path}")
        print(f"🔍 本地模型是否存在: {os.path.exists(local_model_path)}")
        
        if os.path.exists(local_model_path):
            print("✅ 使用本地模型，跳过下载")
            # 直接使用transformers加载本地模型
            from transformers import AutoModel as HFAutoModel, AutoTokenizer as HFAutoTokenizer
            self.roberta_model = HFAutoModel.from_pretrained(local_model_path, local_files_only=True)
            self.roberta_tokenizer = HFAutoTokenizer.from_pretrained(local_model_path, local_files_only=True)
        else:
            print("❌ 本地模型不存在，使用ModelScope下载")
            if config.use_modelscope:
                from modelscope import AutoModel as MSAutoModel, AutoTokenizer as MSAutoTokenizer
                self.roberta_model = MSAutoModel.from_pretrained(
                    config.roberta_model_name,
                    cache_dir=config.modelscope_cache_dir
                )
                self.roberta_tokenizer = MSAutoTokenizer.from_pretrained(
                    config.roberta_model_name,
                    cache_dir=config.modelscope_cache_dir
                )
            else:
                self.roberta_model = AutoModel.from_pretrained(config.roberta_model_name)
                self.roberta_tokenizer = AutoTokenizer.from_pretrained(config.roberta_model_name)
        
        # 跨模态注意力
        self.cross_attention = CrossModalAttention(
            config.clip_feature_dim,
            config.roberta_feature_dim,
            config.num_attention_heads
        )
        
        # 提示学习层
        self.prompt_layer = PromptLearningLayer(config)
        
        # 特征投影层
        self.clip_projection = nn.Linear(config.clip_feature_dim, config.fusion_hidden_dim)
        self.roberta_projection = nn.Linear(config.roberta_feature_dim, config.fusion_hidden_dim)
        self.prompt_projection = nn.Linear(config.roberta_feature_dim, config.fusion_hidden_dim)
        
        # 融合层（暂时只使用3个投影特征，保持兼容性）
        fusion_input_dim = config.fusion_hidden_dim * 3  # 暂时不加真假分数
        self.fusion_layer = nn.Sequential(
            nn.Linear(fusion_input_dim, config.fusion_hidden_dim),
            nn.ReLU(),
            nn.Dropout(config.dropout_rate),
            nn.Linear(config.fusion_hidden_dim, config.fusion_hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout_rate)
        )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(config.fusion_hidden_dim // 2, config.num_classes),
            nn.Dropout(config.dropout_rate)
        )
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        def init_module(module):
            for layer in module:
                if isinstance(layer, nn.Linear):
                    nn.init.xavier_uniform_(layer.weight)
                    if layer.bias is not None:
                        nn.init.zeros_(layer.bias)
        
        # 初始化各个模块
        init_module(self.fusion_layer)
        init_module(self.classifier)
        
        # 单独初始化投影层
        for layer in [self.clip_projection, self.roberta_projection, self.prompt_projection]:
            if isinstance(layer, nn.Linear):
                nn.init.xavier_uniform_(layer.weight)
                if layer.bias is not None:
                    nn.init.zeros_(layer.bias)
    
    def forward(self, inputs):
        """前向传播"""
        images = inputs['images']
        texts = inputs['texts']
        enhanced_texts = inputs.get('enhanced_texts', texts)
        
        batch_size = images.size(0)
        
        # CLIP特征提取
        with torch.no_grad():
            clip_image_features = self.clip_model.encode_image(images)
            clip_text_features = self.clip_model.encode_text(
                clip.tokenize(texts, truncate=True).to(self.config.device)
            )
        
        clip_image_features = clip_image_features.float()
        clip_text_features = clip_text_features.float()
        
        # RoBERTa特征提取
        roberta_inputs = self.roberta_tokenizer(
            enhanced_texts,
            padding=True,
            truncation=True,
            max_length=self.config.pad_size,
            return_tensors='pt'
        ).to(self.config.device)
        
        roberta_outputs = self.roberta_model(**roberta_inputs)
        roberta_features = roberta_outputs.pooler_output
        
        # 提示学习特征
        prompt_features = self.prompt_layer(enhanced_texts, inputs.get('image_descriptions', [''] * batch_size))

        # 注意prompt_features现在包含了真假分数
        # 我们可以单独提取这些分数
        veracity_scores = prompt_features[:, -2:]  # 最后两个维度是真假分数
        prompt_embeddings = prompt_features[:, :-2]  # 其余是嵌入特征

        # 跨模态注意力
        attended_clip_features, attended_roberta_features = self.cross_attention(
            clip_image_features, roberta_features
        )
        
        # 特征投影
        clip_proj = self.clip_projection(attended_clip_features)
        roberta_proj = self.roberta_projection(attended_roberta_features)
        prompt_proj = self.prompt_projection(prompt_embeddings)

        # 特征融合（暂时不使用真假分数，保持兼容性）
        fused_features = torch.cat([clip_proj, roberta_proj, prompt_proj], dim=1)
        fused_features = self.fusion_layer(fused_features)
        
        # 分类
        logits = self.classifier(fused_features)
        
        return logits

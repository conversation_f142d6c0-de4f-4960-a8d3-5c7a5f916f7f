import torch
import torch.nn as nn
import os
from transformers import AutoModelForMaskedLM, AutoTokenizer

class PromptLearningLayer(nn.Module):
    """提示学习层"""
    
    def __init__(self, config):
        super(PromptLearningLayer, self).__init__()
        self.config = config
        
        # 使用RoBERTa作为提示学习的基础模型
        local_model_path = os.path.join(config.modelscope_cache_dir, 'iic', 'nlp_roberta_backbone_large_std')
        
        if os.path.exists(local_model_path):
            print("✅ 提示学习使用本地模型")
            from transformers import AutoModelForMaskedLM, AutoTokenizer
            self.prompt_model = AutoModelForMaskedLM.from_pretrained(local_model_path, local_files_only=True)
            self.prompt_tokenizer = AutoTokenizer.from_pretrained(local_model_path, local_files_only=True)
        else:
            print("❌ 提示学习本地模型不存在，使用在线下载")
            if config.use_modelscope:
                from modelscope import AutoModelForMaskedLM, AutoTokenizer
                self.prompt_model = AutoModelForMaskedLM.from_pretrained(
                    config.roberta_model_name,
                    cache_dir=config.modelscope_cache_dir
                )
                self.prompt_tokenizer = AutoTokenizer.from_pretrained(
                    config.roberta_model_name,
                    cache_dir=config.modelscope_cache_dir
                )
            else:
                from transformers import AutoModelForMaskedLM, AutoTokenizer
                self.prompt_model = AutoModelForMaskedLM.from_pretrained(config.roberta_model_name)
                self.prompt_tokenizer = AutoTokenizer.from_pretrained(config.roberta_model_name)
        
        # 软提示参数
        self.soft_prompt_length = 10
        self.soft_prompt = nn.Parameter(
            torch.randn(self.soft_prompt_length, config.roberta_feature_dim)
        )
        
        # 提示模板
        self.template = config.prompt_template
        
        # 谣言相关词汇
        self.true_tokens = ["真实", "可信", "准确", "属实", "确凿"]
        self.fake_tokens = ["虚假", "谣言", "不实", "捏造", "造谣"]
        
        # 获取这些词的token_id
        self.true_token_ids = self._get_token_ids(self.true_tokens)
        self.fake_token_ids = self._get_token_ids(self.fake_tokens)

        # 验证token数量
        if len(self.true_token_ids) == 0 or len(self.fake_token_ids) == 0:
            print("⚠️ 警告：部分真假token未找到，可能影响提示学习效果")
        
    def _get_token_ids(self, word_list):
        """获取词汇的token_id列表"""
        token_ids = []
        for word in word_list:
            ids = self.prompt_tokenizer.encode(word, add_special_tokens=False)
            if len(ids) >= 1:  # 取第一个token（主要token）
                token_ids.append(ids[0])

        # 如果没有找到任何token，使用一些通用的token作为备选
        if not token_ids:
            # 使用一些常见的中文字符作为备选
            backup_words = ["是", "的", "了", "在", "有"]
            for word in backup_words:
                ids = self.prompt_tokenizer.encode(word, add_special_tokens=False)
                if len(ids) >= 1:
                    token_ids.append(ids[0])
                if len(token_ids) >= 3:  # 至少保证有3个token
                    break

        return token_ids
        
    def forward(self, texts, image_descriptions):
        """
        Args:
            texts: 原始文本列表
            image_descriptions: 图像描述列表
        """
        batch_size = len(texts)
        
        # 构建提示文本
        prompt_texts = []
        for i in range(batch_size):
            text = texts[i] if i < len(texts) else ""
            img_desc = image_descriptions[i] if i < len(image_descriptions) else ""
            
            prompt_text = self.template.format(
                text=text,
                image_desc=img_desc
            )
            prompt_texts.append(prompt_text)
        
        # 编码提示文本
        inputs = self.prompt_tokenizer(
            prompt_texts,
            padding=True,
            truncation=True,
            max_length=self.config.pad_size,
            return_tensors='pt'
        ).to(self.config.device)
        
        # 获取[MASK]的位置
        mask_token_id = self.prompt_tokenizer.mask_token_id
        mask_positions = (inputs.input_ids == mask_token_id).nonzero(as_tuple=True)
        
        # 获取模型输出（包含隐藏状态）
        outputs = self.prompt_model(**inputs, output_hidden_states=True)
        
        # 提取[MASK]位置的预测
        mask_logits = outputs.logits[mask_positions[0], mask_positions[1], :]
        
        # 计算真假类别的概率
        if len(self.true_token_ids) > 0 and len(self.fake_token_ids) > 0:
            true_logits = torch.stack([mask_logits[:, token_id].mean() for token_id in self.true_token_ids])
            fake_logits = torch.stack([mask_logits[:, token_id].mean() for token_id in self.fake_token_ids])

            true_score = true_logits.mean()
            fake_score = fake_logits.mean()
        else:
            # 如果没有找到合适的token，使用随机初始化的分数
            print("⚠️ 警告：未找到合适的真假token，使用默认分数")
            true_score = torch.tensor(0.0, device=self.config.device)
            fake_score = torch.tensor(0.0, device=self.config.device)
        
        # 获取特征
        pooler_output = outputs.hidden_states[-1][:, 0, :]  # [CLS]的表示
        
        # 添加软提示的影响
        soft_prompt_mean = self.soft_prompt.mean(dim=0).unsqueeze(0).expand(batch_size, -1)
        enhanced_features = pooler_output + 0.1 * soft_prompt_mean
        
        # 添加真假分数作为额外特征
        veracity_scores = torch.stack([true_score, fake_score]).unsqueeze(0).expand(batch_size, -1)
        final_features = torch.cat([enhanced_features, veracity_scores], dim=1)
        
        return final_features

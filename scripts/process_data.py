#!/usr/bin/env python3
"""
数据处理脚本
"""

import os
import sys

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.config import Config
from src.data.preprocessing import DataPreprocessor

def main():
    """处理数据"""
    config = Config()
    preprocessor = DataPreprocessor(config)
    
    # 检查原始数据文件是否存在
    required_files = [
        './data/tweets/train_rumor.txt',
        './data/tweets/train_nonrumor.txt', 
        './data/tweets/test_rumor.txt',
        './data/tweets/test_nonrumor.txt'
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print("❌ 缺少以下原始数据文件:")
        for f in missing_files:
            print(f"  {f}")
        print("\n如果您已有处理好的CSV文件，可以直接使用。")
        
        # 检查现有CSV文件
        csv_files = ['./data/train.csv', './data/val.csv', './data/test.csv']
        existing_csv = [f for f in csv_files if os.path.exists(f)]
        
        if existing_csv:
            print(f"\n✅ 发现已处理的CSV文件: {existing_csv}")
            preprocessor.check_data_integrity()
        else:
            print("\n❌ 也没有找到处理好的CSV文件")
            
    else:
        print("✅ 发现所有原始数据文件")
        
        # 询问是否重新处理
        choice = input("是否重新处理原始数据？(y/n): ").lower().strip()
        
        if choice == 'y':
            print("🔄 开始重新处理原始数据...")
            preprocessor.process_all_data()
        else:
            print("📊 检查现有数据...")
            preprocessor.check_data_integrity()

if __name__ == "__main__":
    main()
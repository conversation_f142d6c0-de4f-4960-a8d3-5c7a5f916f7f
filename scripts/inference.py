#!/usr/bin/env python3
"""
单样本推理脚本
"""

import os
import sys
import torch
import numpy as np
from PIL import Image
import clip

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 设置环境变量
os.environ["TOKENIZERS_PARALLELISM"] = "false"

from src.config import Config
from src.models import EnhancedMultimodalNet

class NewsDetector:
    """新闻检测器"""
    
    def __init__(self, model_path=None):
        self.config = Config()
        
        # 加载模型
        self.model = EnhancedMultimodalNet(self.config)
        self.model = self.model.to(self.config.device)
        
        # 使用配置中的路径
        if model_path is None:
            model_path = self.config.save_path
        
        if os.path.exists(model_path):
            self._load_model_with_compatibility(model_path)
        else:
            # 尝试其他可用模型
            alternative_paths = [
                'outputs/checkpoints/best_model_best_acc.pth',
                'outputs/checkpoints/best_model_best_f1.pth',
            ]

            loaded = False
            for alt_path in alternative_paths:
                if os.path.exists(alt_path):
                    self._load_model_with_compatibility(alt_path)
                    loaded = True
                    break

            if not loaded:
                print("⚠️ 未找到训练好的模型，使用随机初始化的模型")
                print("请先运行训练脚本: python scripts/train.py")
        
    def _load_model_with_compatibility(self, model_path):
        """兼容性模型加载"""
        try:
            # 尝试直接加载
            state_dict = torch.load(model_path, map_location=self.config.device)
            self.model.load_state_dict(state_dict, strict=False)
            print(f"✅ 模型加载成功: {model_path}")
        except Exception as e:
            print(f"⚠️ 模型加载遇到兼容性问题: {e}")
            print("这可能是因为模型结构已更新，将使用部分权重初始化")

            # 尝试部分加载
            try:
                state_dict = torch.load(model_path, map_location=self.config.device)
                model_dict = self.model.state_dict()

                # 过滤掉不匹配的键
                filtered_dict = {}
                for k, v in state_dict.items():
                    if k in model_dict and v.shape == model_dict[k].shape:
                        filtered_dict[k] = v
                    else:
                        print(f"跳过不兼容的权重: {k}")

                model_dict.update(filtered_dict)
                self.model.load_state_dict(model_dict)
                print(f"✅ 部分模型权重加载成功: {model_path}")
            except Exception as e2:
                print(f"❌ 模型加载失败: {e2}")
                print("将使用随机初始化的模型")

        # 加载CLIP预处理
        _, self.clip_preprocess = clip.load(self.config.clip_model_name, device=self.config.device)
        
        self.model.eval()
    
    def predict(self, text, image_path=None):
        """预测单个样本"""
        with torch.no_grad():
            # 处理文本
            if isinstance(text, str):
                texts = [text]
            else:
                texts = text
            
            # 处理图像
            if image_path and os.path.exists(image_path):
                image = Image.open(image_path).convert('RGB')
                image = self.clip_preprocess(image).unsqueeze(0)
            else:
                # 创建空白图像
                image = torch.zeros(1, 3, 224, 224)
            
            # 移到设备
            image = image.to(self.config.device)
            
            # 构建输入
            inputs = {
                'images': image,
                'texts': texts,
                'enhanced_texts': texts,  # 简化版本
                'image_descriptions': [''] * len(texts),
                'emotion_factors': [1.0] * len(texts)
            }
            
            # 预测
            outputs = self.model(inputs)
            probabilities = torch.softmax(outputs, dim=1)
            predicted = torch.argmax(outputs, dim=1)

            # 返回结果
            result = {
                'prediction': '虚假' if predicted[0].item() == 1 else '真实',
                'confidence': probabilities[0].max().item(),
                'probabilities': {
                    '真实': probabilities[0][0].item(),
                    '虚假': probabilities[0][1].item()
                }
            }
            
            return result

def main():
    """主函数"""
    print("🔍 新闻真实性检测器")
    print("="*30)
    
    # 初始化检测器
    try:
        detector = NewsDetector()
    except FileNotFoundError as e:
        print(f"❌ {e}")
        print("请先训练模型或提供正确的模型路径")
        return
    
    # 示例检测
    sample_text = "某地发生重大事故，造成多人伤亡"
    sample_image = None  # 可以提供图片路径
    
    print(f"📝 检测文本: {sample_text}")
    
    result = detector.predict(sample_text, sample_image)
    
    print(f"\n📊 检测结果:")
    print(f"预测类别: {result['prediction']}")
    print(f"置信度: {result['confidence']:.4f}")
    print(f"详细概率:")
    for label, prob in result['probabilities'].items():
        print(f"  {label}: {prob:.4f}")

if __name__ == '__main__':
    main()

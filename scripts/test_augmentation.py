#!/usr/bin/env python3
"""
测试专利级数据增强功能
"""

import os
import sys

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.config import Config
from src.data.augmentation import PatentLLMDataAugmentation


def test_single_text_augmentation():
    """测试单条文本的增强功能"""
    print("🧪 测试单条文本增强功能")
    print("=" * 50)
    
    # 初始化
    config = Config()
    augmenter = PatentLLMDataAugmentation(config)
    
    # 测试文本
    test_text = "某地发生重大交通事故，造成多人伤亡，现场情况十分严重。"
    
    print(f"📝 原始文本: {test_text}")
    print()
    
    # 测试API连接
    print("🔗 测试API连接...")
    if not augmenter.test_api_connection('deepseek'):
        print("❌ API连接失败，将使用缓存或跳过测试")
        return
    
    # 生成各类增强
    print("🔄 生成Waug1 (规则化文本操作)...")
    waug1 = augmenter.generate_waug1(test_text)
    print(f"📄 Waug1: {waug1}")
    print()
    
    print("🔄 生成Waug2 (多样化重描述)...")
    waug2 = augmenter.generate_waug2(test_text)
    print(f"📄 Waug2: {waug2}")
    print()
    
    print("🔄 生成Waug3 (图像详细描述)...")
    waug3 = augmenter.generate_waug3(test_text)
    print(f"📄 Waug3: {waug3}")
    print()
    
    # 生成完整增强
    print("🔄 生成完整专利级增强...")
    full_result = augmenter.generate_patent_augmentation(test_text)
    print(f"📄 融合增强: {full_result['waug_fused']}")
    print()
    
    # 情感分析
    emotion_factor = augmenter.get_emotion_factor(test_text)
    print(f"😊 情感因子: {emotion_factor:.3f}")
    print()
    
    # 缓存统计
    stats = augmenter.get_cache_stats()
    print("📊 缓存统计:")
    for key, value in stats.items():
        print(f"  {key}: {value}")


def test_batch_augmentation():
    """测试批量增强功能"""
    print("\n🧪 测试批量增强功能")
    print("=" * 50)
    
    # 初始化
    config = Config()
    augmenter = PatentLLMDataAugmentation(config)
    
    # 测试文本列表
    test_texts = [
        "某地发生重大交通事故，造成多人伤亡。",
        "专家表示，这项新技术将改变行业格局。",
        "网传某明星涉嫌违法，经纪公司紧急辟谣。"
    ]
    
    print(f"📝 测试文本数量: {len(test_texts)}")
    
    # 批量增强
    print("🔄 开始批量增强...")
    results = augmenter.batch_augment_dataset(test_texts, max_workers=2)
    
    # 显示结果
    for i, result in enumerate(results):
        print(f"\n📄 文本 {i+1}:")
        print(f"  原始: {result['original'][:50]}...")
        print(f"  Waug1: {result['waug1'][:50]}...")
        print(f"  Waug2: {result['waug2'][:50]}...")
        print(f"  融合: {result['waug_fused'][:50]}...")


def test_cache_functionality():
    """测试缓存功能"""
    print("\n🧪 测试缓存功能")
    print("=" * 50)
    
    # 初始化
    config = Config()
    augmenter = PatentLLMDataAugmentation(config)
    
    test_text = "测试缓存功能的示例文本。"
    
    # 第一次生成（应该调用API）
    print("🔄 第一次生成（调用API）...")
    import time
    start_time = time.time()
    result1 = augmenter.generate_waug1(test_text)
    time1 = time.time() - start_time
    print(f"⏱️  耗时: {time1:.2f}秒")
    
    # 第二次生成（应该使用缓存）
    print("🔄 第二次生成（使用缓存）...")
    start_time = time.time()
    result2 = augmenter.generate_waug1(test_text)
    time2 = time.time() - start_time
    print(f"⏱️  耗时: {time2:.2f}秒")
    
    # 验证结果一致性
    if result1 == result2:
        print("✅ 缓存功能正常，结果一致")
    else:
        print("❌ 缓存功能异常，结果不一致")
    
    print(f"🚀 缓存加速比: {time1/time2:.1f}x" if time2 > 0 else "🚀 缓存加速显著")


def main():
    """主测试函数"""
    print("🎯 专利级数据增强功能测试")
    print("=" * 60)
    
    try:
        # 测试单条文本增强
        test_single_text_augmentation()
        
        # 测试批量增强
        test_batch_augmentation()
        
        # 测试缓存功能
        test_cache_functionality()
        
        print("\n🎉 所有测试完成！")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()

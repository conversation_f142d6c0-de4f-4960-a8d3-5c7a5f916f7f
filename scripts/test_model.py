#!/usr/bin/env python3
"""
模型测试脚本
"""

import os
import sys
import torch
import numpy as np
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix, classification_report
from torch.utils.data import DataLoader

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.config import Config
from src.models import EnhancedMultimodalNet
from src.data import EnhancedDataset, enhanced_collate_fn

def test_model():
    """测试模型性能"""
    print("🧪 开始模型测试...")
    
    # 加载配置
    config = Config()
    
    # 加载最佳模型
    model = EnhancedMultimodalNet(config)
    model = model.to(config.device)
    
    # 尝试加载最佳F1模型
    model_path = 'outputs/checkpoints/best_model_best_f1.pth'
    try:
        if os.path.exists(model_path):
            state_dict = torch.load(model_path, map_location=config.device)
            model.load_state_dict(state_dict, strict=False)
            print(f"✅ 加载模型: {model_path}")
        else:
            # 尝试其他模型
            alt_path = 'outputs/checkpoints/best_model_best_acc.pth'
            if os.path.exists(alt_path):
                state_dict = torch.load(alt_path, map_location=config.device)
                model.load_state_dict(state_dict, strict=False)
                print(f"✅ 使用备用模型: {alt_path}")
            else:
                print(f"❌ 未找到训练好的模型")
                print("请先运行训练脚本: python scripts/train.py")
                return
    except Exception as e:
        print(f"⚠️ 模型加载遇到问题: {e}")
        print("将尝试部分加载...")
        try:
            state_dict = torch.load(model_path, map_location=config.device)
            model_dict = model.state_dict()
            filtered_dict = {k: v for k, v in state_dict.items()
                           if k in model_dict and v.shape == model_dict[k].shape}
            model_dict.update(filtered_dict)
            model.load_state_dict(model_dict)
            print(f"✅ 部分模型权重加载成功")
        except Exception as e2:
            print(f"❌ 模型加载失败: {e2}")
            return
    
    model.eval()
    
    # 加载测试数据
    test_dataset = EnhancedDataset(config.test_path, config, is_train=False)
    test_loader = DataLoader(
        test_dataset, 
        batch_size=config.batch_size,
        shuffle=False,
        collate_fn=enhanced_collate_fn
    )
    
    print(f"📊 测试集样本数: {len(test_dataset)}")
    
    # 测试
    all_preds = []
    all_labels = []
    total_loss = 0
    criterion = torch.nn.CrossEntropyLoss()
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(test_loader):
            inputs = {
                'images': batch['images'].to(config.device),
                'texts': batch['texts'],
                'enhanced_texts': batch['enhanced_texts'],
                'image_descriptions': batch['image_descriptions'],
                'emotion_factors': batch['emotion_factors']
            }
            labels = batch['labels'].to(config.device)
            
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            total_loss += loss.item()
            
            preds = torch.argmax(outputs, dim=1)
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            
            if batch_idx % 10 == 0:
                print(f"处理批次: {batch_idx}/{len(test_loader)}")
    
    # 计算指标
    avg_loss = total_loss / len(test_loader)
    accuracy = accuracy_score(all_labels, all_preds)
    precision, recall, f1, _ = precision_recall_fscore_support(
        all_labels, all_preds, average='weighted'
    )
    
    print("\n" + "="*50)
    print("🎯 测试集最终结果")
    print("="*50)
    print(f"损失 (Loss): {avg_loss:.4f}")
    print(f"准确率 (Accuracy): {accuracy:.4f}")
    print(f"精确率 (Precision): {precision:.4f}")
    print(f"召回率 (Recall): {recall:.4f}")
    print(f"F1分数: {f1:.4f}")
    
    # 混淆矩阵
    cm = confusion_matrix(all_labels, all_preds)
    print(f"\n📊 混淆矩阵:")
    print(f"真实\\预测    真实    虚假")
    print(f"真实        {cm[0,0]:4d}    {cm[0,1]:4d}")
    print(f"虚假        {cm[1,0]:4d}    {cm[1,1]:4d}")
    
    # 各类别详细指标
    precision_per_class, recall_per_class, f1_per_class, _ = precision_recall_fscore_support(
        all_labels, all_preds, average=None
    )
    
    print(f"\n📈 各类别详细指标:")
    print(f"真实新闻 - 精确率: {precision_per_class[0]:.4f}, 召回率: {recall_per_class[0]:.4f}, F1: {f1_per_class[0]:.4f}")
    print(f"虚假新闻 - 精确率: {precision_per_class[1]:.4f}, 召回率: {recall_per_class[1]:.4f}, F1: {f1_per_class[1]:.4f}")
    
    # 详细分类报告
    print(f"\n📋 详细分类报告:")
    target_names = ['真实新闻', '虚假新闻']
    print(classification_report(all_labels, all_preds, target_names=target_names))
    
    return {
        'loss': avg_loss,
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'confusion_matrix': cm
    }

if __name__ == '__main__':
    test_model()

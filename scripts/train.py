#!/usr/bin/env python3
"""
增强多模态虚假新闻检测系统
基于专利技术实现的CLIP+LLM增强架构
"""

import os
import sys
import torch
import numpy as np
from torch.utils.data import DataLoader

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 设置环境变量，避免tokenizers警告
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# 导入模块
from src.config import Config
from src.models import EnhancedMultimodalNet
from src.data import EnhancedDataset, enhanced_collate_fn
from src.training import Trainer
from src.utils.logger import setup_logger

def setup_environment(config):
    """设置训练环境"""
    # 设置随机种子
    np.random.seed(42)
    torch.manual_seed(42)
    torch.cuda.manual_seed_all(42)
    torch.backends.cudnn.deterministic = True
    
    # 设置日志
    logger = setup_logger(config.log_dir)
    return logger

def load_data(config):
    """加载数据"""
    print("📊 加载增强数据集...")
    
    train_dataset = EnhancedDataset(config.train_path, config, is_train=True)
    val_dataset = EnhancedDataset(config.val_path, config, is_train=True)
    test_dataset = EnhancedDataset(config.test_path, config, is_train=True)
    
    train_loader = DataLoader(
        train_dataset, 
        batch_size=config.batch_size, 
        shuffle=True, 
        collate_fn=enhanced_collate_fn,
        num_workers=0
    )
    val_loader = DataLoader(
        val_dataset, 
        batch_size=config.batch_size, 
        shuffle=False, 
        collate_fn=enhanced_collate_fn,
        num_workers=0
    )
    test_loader = DataLoader(
        test_dataset, 
        batch_size=config.batch_size, 
        shuffle=False, 
        collate_fn=enhanced_collate_fn,
        num_workers=0
    )
    
    return train_loader, val_loader, test_loader

def main():
    """主函数"""
    print("🎯 启动增强多模态虚假新闻检测系统")
    print("=" * 50)
    
    # 加载配置
    config = Config()
    print(f"🔧 配置加载完成，使用设备: {config.device}")
    
    # 设置环境
    logger = setup_environment(config)
    
    # 加载数据
    train_loader, val_loader, test_loader = load_data(config)
    
    # 初始化模型
    print("🤖 初始化增强多模态网络...")
    model = EnhancedMultimodalNet(config)
    model = model.to(config.device)
    
    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"📊 模型参数量: {total_params:,}")
    
    # 初始化训练器
    trainer = Trainer(config, model, train_loader, val_loader, test_loader)
    
    # 开始训练
    trainer.train()
    
    print("🎉 训练完成!")

if __name__ == '__main__':
    main()

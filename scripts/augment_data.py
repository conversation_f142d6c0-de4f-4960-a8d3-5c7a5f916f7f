#!/usr/bin/env python3
"""
专利级数据增强脚本
基于发明专利实现三类提示词数据增强：Waug1、Waug2、Waug3
支持本地缓存，避免重复API调用
"""

import os
import sys
import argparse
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.config import Config
from src.data.augmentation import PatentLLMDataAugmentation


def main():
    parser = argparse.ArgumentParser(description='专利级数据增强工具')
    parser.add_argument('--input', '-i', required=True, help='输入CSV文件路径')
    parser.add_argument('--output', '-o', required=True, help='输出CSV文件路径')
    parser.add_argument('--text-column', default='text', help='文本列名 (默认: text)')
    parser.add_argument('--workers', '-w', type=int, default=4, help='并行线程数 (默认: 4)')
    parser.add_argument('--api-type', choices=['deepseek', 'gpt4', 'qwen'], 
                       default='deepseek', help='使用的API类型 (默认: deepseek)')
    parser.add_argument('--no-cache', action='store_true', help='不使用缓存')
    parser.add_argument('--clear-cache', action='store_true', help='清理缓存后退出')
    parser.add_argument('--test-api', action='store_true', help='测试API连接后退出')
    parser.add_argument('--cache-stats', action='store_true', help='显示缓存统计信息')
    
    args = parser.parse_args()
    
    # 初始化配置和增强器
    print("🔧 初始化专利级数据增强系统...")
    config = Config()
    augmenter = PatentLLMDataAugmentation(config)
    
    # 处理特殊命令
    if args.clear_cache:
        augmenter.clear_cache()
        return
    
    if args.test_api:
        success = augmenter.test_api_connection(args.api_type)
        if not success:
            print("❌ API连接失败，请检查配置")
            sys.exit(1)
        return
    
    if args.cache_stats:
        stats = augmenter.get_cache_stats()
        print("📊 缓存统计信息:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        return
    
    # 验证输入文件
    if not os.path.exists(args.input):
        print(f"❌ 输入文件不存在: {args.input}")
        sys.exit(1)
    
    # 创建输出目录
    output_dir = os.path.dirname(args.output)
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # 测试API连接
    print(f"🔗 测试 {args.api_type} API连接...")
    if not augmenter.test_api_connection(args.api_type):
        print("❌ API连接失败，请检查API配置")
        print("提示：请在 src/config.py 中配置正确的API密钥")
        sys.exit(1)
    
    # 显示缓存状态
    if not args.no_cache:
        stats = augmenter.get_cache_stats()
        print(f"💾 当前缓存状态: {stats['total_cached_items']} 条已缓存")
    
    # 开始数据增强
    print("🚀 开始专利级数据增强...")
    start_time = time.time()
    
    try:
        augmenter.augment_csv_dataset(
            input_csv=args.input,
            output_csv=args.output,
            text_column=args.text_column,
            max_workers=args.workers,
            use_cache=not args.no_cache
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"🎉 数据增强完成！")
        print(f"⏱️  总耗时: {duration:.2f} 秒")
        print(f"📁 输出文件: {args.output}")
        
        # 显示最终缓存统计
        if not args.no_cache:
            final_stats = augmenter.get_cache_stats()
            print(f"💾 最终缓存: {final_stats['total_cached_items']} 条")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 数据增强失败: {e}")
        sys.exit(1)


def show_usage_examples():
    """显示使用示例"""
    print("""
📖 使用示例:

1. 基本用法 - 增强训练数据:
   python scripts/augment_data.py -i data/train.csv -o data/train_augmented.csv

2. 指定文本列和线程数:
   python scripts/augment_data.py -i data/train.csv -o data/train_augmented.csv --text-column content --workers 8

3. 使用不同的API:
   python scripts/augment_data.py -i data/train.csv -o data/train_augmented.csv --api-type gpt4

4. 不使用缓存 (每次重新生成):
   python scripts/augment_data.py -i data/train.csv -o data/train_augmented.csv --no-cache

5. 管理缓存:
   python scripts/augment_data.py --cache-stats     # 查看缓存统计
   python scripts/augment_data.py --clear-cache     # 清理缓存

6. 测试API连接:
   python scripts/augment_data.py --test-api --api-type deepseek

📝 注意事项:
- 首次运行会调用API生成增强文本，后续运行会使用缓存
- 建议先用小数据集测试API连接和效果
- 大数据集建议使用多线程 (--workers 参数)
- API调用可能产生费用，请注意控制用量
""")


if __name__ == '__main__':
    if len(sys.argv) == 1:
        show_usage_examples()
    else:
        main()
